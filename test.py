# from utils.utils import ArgsObject
# from server import models


# args = ArgsObject()
# args.llm_name = "QIANFAN"
#     # 模型初始化
# llm = getattr(models, args.llm_name)(args).llm

# print(llm("你好"))

from pathlib import Path
from paddleocr import PPStructure

input_file = "./your_pdf_file.pdf"
output_path = Path("./output")

pipeline = PPStructure()
output = pipeline.predict(input=input_file)

markdown_list = []
markdown_images = []

for res in output:
    md_info = res.markdown
    markdown_list.append(md_info)
    markdown_images.append(md_info.get("markdown_images", {}))

markdown_texts = pipeline.concatenate_markdown_pages(markdown_list)

mkd_file_path = output_path / f"{Path(input_file).stem}.md"
mkd_file_path.parent.mkdir(parents=True, exist_ok=True)

with open(mkd_file_path, "w", encoding="utf-8") as f:
    f.write(markdown_texts)

for item in markdown_images:
    if item:
        for path, image in item.items():
            file_path = output_path / path
            file_path.parent.mkdir(parents=True, exist_ok=True)
            image.save(file_path)

