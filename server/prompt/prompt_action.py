from langchain.prompts import PromptTemplate
from server.parser.parser_action import FORMAT_INSTRUCTIONS

template = """
## 指令
请完成意图识别任务，已知的意图有["查数据"，"生成待办","无意图"]，分别对应的函数如下：
{{"查数据": "search_data", "生成待办": "create_todo", "无意图": "non"}}

{format_instructions}

## 示例
【输入】 查询张三的目前的待办事项
【返回】{{"intention": "查数据", "function": "search_data"}}

【输入】 帮我创建待办，项目为集团收购计划，责任人张三
【返回】{{"intention": "生成待办", "function": "create_todo"}}

【输入】 你好
【返回】{{"intention": "无意图", "function": "non"}}

请输出包含 intention 和 function 的结果
【输入】
{context}
"""

PROMPT = PromptTemplate(
    input_variables=["context"],
    partial_variables={"format_instructions": FORMAT_INSTRUCTIONS},
    template=template,
)
