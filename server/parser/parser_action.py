from langchain.output_parsers import StructuredOutputParser, ResponseSchema

# 大模型回复结构
response_schema = [
    ResponseSchema(type="string", name="intention", description="用户的意图"),
    ResponseSchema(type="string", name="function", description="意图对应的function")
]

output_parsers = StructuredOutputParser.from_response_schemas(response_schema)

FORMAT_INSTRUCTIONS = output_parsers.get_format_instructions(only_json=True)