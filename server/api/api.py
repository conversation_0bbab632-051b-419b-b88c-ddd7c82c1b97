from fastapi.routing import APIRouter

from .user import user_api
from .model_extract import model_extract_api
from .llm_label import llm_label_api
from .llm_qa import llm_qa_api




api_router = APIRouter()

api_router.include_router(user_api, prefix="/user")
api_router.include_router(model_extract_api, prefix="/kg")
api_router.include_router(llm_label_api, prefix="/kg")
api_router.include_router(llm_qa_api, prefix="/kg")
# api_router.include_router(market_api, prefix="/market")

__all__ = ['api_router']
