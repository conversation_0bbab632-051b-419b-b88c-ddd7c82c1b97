from server.parser.parser_extract import extract_format_dynamic_instructions
from langchain.prompts import PromptTemplate

def extract_prompt_and_format(prompt, file, extracts):
    input_prompt = PromptTemplate(
        input_variables=["file"],
        partial_variables={"format_instructions": extract_format_dynamic_instructions(extracts)},
        template=prompt
    )
    print(input_prompt)
    return input_prompt.format(file=file)
