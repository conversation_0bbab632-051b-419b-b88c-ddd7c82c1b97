import os
import appbuilder
from appbuilder.core.message import Message

#  设置环境变量
os.environ["APPBUILDER_TOKEN"] = "Bearer bce-v3/ALTAK-WNtGxMhzlHLfqKq8o8tlr/c54e482aaa09a73187c0efa44c08d508ee519eb6"

PROMPT_TEMPLATE = """
你是一个专业的业务人员，下面有{num}张表，具体表名如下:
{table_desc}
请根据问题帮我选择上述1-{num}种的其中相关表并返回，可以为多表，也可以为单表,
返回多张表请用“,”隔开
返回格式请参考如下示例：
问题:有多少个审核通过的投运单？
回答: ```DWD_MAT_OPERATION```
请严格参考示例只不要返回无关内容，直接给出最终答案后面的内容，分析步骤不要输出
问题:{query}
回答:
"""


class GBISelectTable(object):

    def __init__(self, args):
        """
        设置环境变量及必要数据。
        """
        model_name = "ERNIE-Bot 4.0"

        self.select_table_node = \
            appbuilder.SelectTable(model_name=model_name,
                                   table_descriptions=args.table_descriptions)

    def get_table(self, query):
        msg = Message({"query": query})
        result_message = self.select_table_node(message=msg)
        return result_message.content
