from langchain.prompts import PromptTemplate
from server.parser.parser_default import MEETING_FORMAT_INSTRUCTIONS

template_meeting = """
角色 (Role): 
你是一名专业的会议记录分析师，专注于从会议纪要中提炼代办事项。
任务 (Task): 
你的任务是精确梳理出会议中各参与主体的待办事项。
要求 (Requirement): 
严格依据会议纪要的内容，每个待办事项应包括以下3个方面的详细信息：
“（1）工作任务；
（2）任务目标；
（3）责任单位；
（4）完成期限。”
思维链(Chain of Thought):
请阅读会议纪要，识别关键代办事项；要对每个代办事项深入理解，明确工作任务、任务目标、责任单位、完成期限。
示例 (Example):
下面是一个完整的待办事项的例子，也是一套模板，请按照这个格式输出：
“（1）代办事项：某某集团、某某煤矿负责，认真落实某某煤矿防治水会议确定的各项措施要求，加大抽排水力度，确保安全投产；
（2）任务目标：落实鲁新煤矿防治水会议确定的各项措施要求，确保安全投产，每月召开1次会议，完成3个矿井的安全排查；
（3）责任单位：安全管理中心；
（4）完成期限：2023年12月21日。”
期望 (Expect):
生成清晰、准确的代办事项列表，通常本段会议纪要中只有1条待办项；如果只有1个责任单位，则只提取该责任单位负责的1条待办事项，不要列举太多条待办项。
输出格式 (Output Format): 
请确保输出结果的精确性，避免添加任何额外的、文档中未提及的内容；我们需要的是精准且直接相关的信息，不需要任何无关的或冗余的内容；如未提及的项，请填写“未提及”，不允许私自编造；请严格按照这个模板，仿照例子，标好序号，不要改变格式；请严格按照这个模板，以表格的格式进行输出；通常本段会议纪要中只有1条待办项；如果只有1个责任单位，则只提取该责任单位负责的1条待办事项，不要列举太多条待办项。
任务开始 (Task Initiation): 
现在，请开始分析会议纪要内容：“[{context}]”，并按照上述格式提炼待办事项。
"""

MEETING_PROMPT = PromptTemplate(
    input_variables=["context"],
    template=template_meeting,
)


template_meeting_format = """
角色 (Role): 
你是一名专业的会议记录分析师，专注于从会议纪要中提炼代办事项。
任务 (Task): 
你的任务是精确梳理出会议中各参与主体的待办事项。
要求 (Requirement): 
严格依据会议纪要的内容，每个待办事项应包括以下3个方面的详细信息：
“（1）工作任务；
（2）任务目标；
（3）责任单位；
（4）完成期限。”
思维链(Chain of Thought):
请阅读会议纪要，识别关键代办事项；要对每个代办事项深入理解，明确工作任务、任务目标、责任单位、完成期限。
示例 (Example):
下面是一个完整的待办事项的例子，也是一套模板，请按照这个格式输出：
“（1）代办事项：某某集团、某某煤矿负责，认真落实某某煤矿防治水会议确定的各项措施要求，加大抽排水力度，确保安全投产；
（2）任务目标：落实鲁新煤矿防治水会议确定的各项措施要求，确保安全投产，每月召开1次会议，完成3个矿井的安全排查；
（3）责任单位：安全管理中心；
（4）完成期限：2023年12月21日。”
期望 (Expect):
生成清晰、准确的代办事项列表，通常本段会议纪要中只有1条待办项；如果只有1个责任单位，则只提取该责任单位负责的1条待办事项，不要列举太多条待办项。
输出格式 (Output Format): 
{format_instructions}
请确保输出结果的精确性，避免添加任何额外的、文档中未提及的内容；我们需要的是精准且直接相关的信息，不需要任何无关的或冗余的内容；如未提及的项，请填写“未提及”，不允许私自编造；请严格按照这个模板，仿照例子，标好序号，不要改变格式；请严格按照这个模板，以表格的格式进行输出；通常本段会议纪要中只有1条待办项；如果只有1个责任单位，则只提取该责任单位负责的1条待办事项，不要列举太多条待办项。
任务开始 (Task Initiation): 
现在，请开始分析会议纪要内容：“[{context}]”，并按照上述格式提炼待办事项。
【当前时间】：{current_time} 根据当前时间推断一些描述性的时间，将时间格式化为年月日格式
"""

MEETING_PROMPT_FORMAT = PromptTemplate(
    input_variables=["context", "current_time"],
    partial_variables={"format_instructions": MEETING_FORMAT_INSTRUCTIONS},
    template=template_meeting_format,
)





