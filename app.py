from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from server.api.api import api_router
from starlette.middleware.cors import CORSMiddleware



def createApp():
    app = FastAPI(title="知识图谱平台模型抽取服务")
    # set middleware
    # register_middleware(app)
    # api router
    app.include_router(api_router, prefix="/api/v1")
    # set socketio
    # app.mount('/', socket_app)
    # set static files
    # app.mount("/media", StaticFiles(directory="media"), name="media")   # 媒体文件
    # allow cross domain
    app.add_middleware(CORSMiddleware, allow_origins=['*'],
                       allow_credentials=True, allow_methods=["*"], allow_headers=["*"])

    # # print all path
    # for _route in app.routes:
    #     r = _route.__dict__
    #     print(r['path'], r.get('methods', {}))
    return app



app = createApp()


if __name__ == '__main__':
    import uvicorn
    # Don't set debug/reload equals True in release, because TimedRotatingFileHandler can't support multi-prcoess
    # please used "uvicorn --host 127.0.0.1 --port 8000 main:app --env-file ./configs/.env" run in release, and used "python main.py" in dev
    uvicorn.run(
        app=app,
        host=str('127.0.0.1'),
        port=8088,
        reload=False
    )
    
    
"""
Celery schedule worker

1) start worker in project base path
    
    celery -A workers  worker -l info
    
2) start beat in project base path

    celery -A workers beat -l info
    
"""