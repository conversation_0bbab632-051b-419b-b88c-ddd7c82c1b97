# GBI使用说明（内部）

# 1.概述

​	生成式商业智能产品，将文心大模型融入BI场景，支持通过自然语言对话式交互，执行数据查询与分析，实现“任意表，随便问”，为企业客户建立“对话即洞察”的数据分析新范式。

# 2. copilot

定位：copilot，与Agent比较更注重能力的输出，比较单一、更专注。

![gbi](./img/GBI.png)

# 3. AppBuilder

安装：

pip install --upgrade appbuilder-sdk

pip install git+https://github.com/baidubce/app-builder.git

获取token

组件中心》创建秘钥

```
# 设置环境变量
os.environ["APPBUILDER_TOKEN"] = "Bearer 秘钥"

```



# 4. 流程

1. 先选表
2. 根据表选择schma
3. 获取sql

4. 执行查询语句
5. 大模型润色

# 5. 规范说明

1. GBI表的Schma带数据类型，不需要带类型长度和是否默认空，减少token数
2. 目前单表处理效果最好，优先考虑单表或者视图处理的形式单独用于GBI处理
3. 默认传递当前时间，用于后续SQL处理
4. 问句条件强调字段，否则GBI不能准确对应字段