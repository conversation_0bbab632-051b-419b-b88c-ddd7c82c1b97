from langchain_core.prompts import PromptTemplate
from server import models
from utils.utils import ArgsObject
from langchain.chains import GraphCypherQAChain
from server.db.neo4j_graph import graph
import sys

CYPHER_GENERATION_TEMPLATE = """
您是Neo4j开发专家，将用户问题翻译成Cypher，并且Cypher语句添加过滤条件（graphId={graphId}），Cypher查询结果除了问题答案还包含docAuth（别名docAuth），回答有关档案的问题并提供建议。
根据schema转换用户的问题。

Schema: {schema}
Question: {question}
"""

# QA_GENERATION_TEMPLATE = """
# 您是档案知识专家，根据问题结合答案润色一下。
#
# 【答案】：{context}
# 【问题】: {question}
# """
#
# CYPHER_GENERATION_TEMPLATE = """Task:Generate Cypher statement to query a graph database.
# Instructions:
# Use only the provided relationship types and properties in the schema.
# Do not use any other relationship types or properties that are not provided.
# Schema:
# {schema}
# Note: Do not include any explanations or apologies in your responses.
# Do not respond to any questions that might ask anything else than for you to construct a Cypher statement.
# Do not include any text except the generated Cypher statement.
# Examples: Here are a few examples of generated Cypher statements for particular questions:
# # How many people played in Top Gun?
# MATCH (m:Movie {{name:"Top Gun"}})<-[:ACTED_IN]-()
# RETURN count(*) AS numberOfActors
#
# The question is:
# {question}
# """

args = ArgsObject()


def llm_qa(query, graphTypes, graphId):

    args.llm_name = "QIANFAN"
    # 模型初始化
    model = getattr(models, args.llm_name)(args).llm

    base_prompt = sub(CYPHER_GENERATION_TEMPLATE)
    # 通用提示词
    cypher_generation_prompt = PromptTemplate(
        template=base_prompt,
        input_variables=["schema", "question"],
    )


    graph.refresh_schema()
    chain = GraphCypherQAChain.from_llm(
        llm=model,
        graph=graph,
        cypher_prompt=cypher_generation_prompt,
        include_types=graphTypes,
        verbose=True,
        validate_cypher=True,
        return_direct=True
    )

    # 图谱schema
    print("graph_schema=========", chain.graph_schema)

    try:
        # 尝试执行一些可能会引发异常的代码
        cypher_result = chain.invoke({"query": query})
        # 结果
        print("cypher_result======", cypher_result["result"])

        qa_result = chain.qa_chain.invoke({"question": query, "context": cypher_result})

        final_result = qa_result[chain.qa_chain.output_key]

        # 结果
        print("final_result======", final_result)

        return {"result": final_result, "question": query, "docAuth": filter_params(cypher_result["result"], "docAuth")}
    except Exception as e:
        # 捕获所有异常，并处理它们
        print(f"An error occurred: {e}")
    return {"result": "对不起，我没有理解您的问题!", "question": query}


def filter_params(params, field):
    filter_params = []
    # 遍历参数字典
    for i, param in enumerate(params):
        if param[field] is not None:
            filter_params.append(param[field])

    return filter_params


def sub(text):
    return text.format_map(safe_sub(sys._getframe(1).f_locals))


class safe_sub(dict):
    def __missing__(self, key):
        return "{"+key+"}"
