from utils.utils import ArgsObject
import os
import json
import torch
import torch.nn as nn
import numpy as np
from torchcrf import CRF
from collections import namedtuple
from seqeval.metrics.sequence_labeling import get_entities
from transformers import BertTokenizer, BertModel, BertConfig

args = ArgsObject()


def model_extract(texts, data_name, model_type=None):
    predictor = Predictor(data_name, model_type)
    result_msg = []
    for text in texts:
        model = {}
        output = {}
        entities = []
        relations = []
        model.setdefault("content", text)

        ner_result = predictor.ner_predict(text)
        print("实体>>>>>：", ner_result)
        to_ner_class(entities, ner_result)
        output.setdefault("entities", entities)
        model.setdefault("output", output)
        if model_type is not None and model_type == "ner":
            result_msg.append(model)
            continue
        re_result = predictor.re_predict(text, ner_result)
        print("关系>>>>>：", re_result)
        to_re_class(relations, ner_result, re_result)
        output.setdefault("relations", relations)
        result_msg.append(model)
    print(result_msg)
    return result_msg

def to_ner_class(result, ner_result):
    for ner in ner_result:
        for v in ner_result[ner]:
            output = NerClass(v[0], v[1], v[2], ner)
            result.append(output.to_json())

def to_re_class(result, ner_result, re_result):
    ner_map = dict()
    for ner in ner_result:
        for v in ner_result[ner]:
            ner_map.setdefault(v[0], ner)
    for re in re_result:
        position = {}
        h = re[3].split("_")
        t = re[4].split("_")
        position.setdefault("s", {"start": int(h[0]), "end": int(h[1])})
        position.setdefault("t", {"start": int(t[0]), "end": int(t[1])})
        output = ReClass(re[0], ner_map[re[0]], re[1], ner_map[re[1]], re[2], position)
        result.append(output.to_json())

def get_args(args_path, args_name=None):
    with open(args_path, "r", encoding="utf-8") as fp:
        args_dict = json.load(fp)
    # 注意args不可被修改了
    args = namedtuple(args_name, args_dict.keys())(*args_dict.values())
    return args


class Predictor:
    def __init__(self, data_name, model_type):
        self.data_name = data_name
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.ner_args = get_args(os.path.join("././model_args/", f"{data_name}_ner_args.json"), "ner_args")
        self.ner_id2label = {int(k): v for k, v in self.ner_args.id2label.items()}
        self.labels = self.ner_args.labels
        self.tokenizer = BertTokenizer.from_pretrained(self.ner_args.bert_dir)
        self.max_seq_len = self.ner_args.max_seq_len
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.ner_model = BertNer(self.ner_args)
        self.ner_model.load_state_dict(torch.load(os.path.join(self.ner_args.output_dir, f"{data_name}_model_ner.bin"), map_location=torch.device('cpu')))
        self.ner_model.to(self.device)
        if model_type is None or model_type == "rel":
            self.re_args = get_args(os.path.join("././model_args/", f"{data_name}_re_args.json"), "re_args")
            self.re_model = BertRe(self.re_args)
            self.re_id2label = {int(k): v for k, v in self.re_args.id2label.items()}
            self.re_model.load_state_dict(torch.load(os.path.join(self.re_args.output_dir, f"{data_name}_model_re.bin"), map_location=torch.device('cpu')))
            self.re_model.to(self.device)
            self.rels = None
            if os.path.exists(os.path.join("././model_args/", f"{data_name}_rels.txt")):
                with open(os.path.join("././model_args/", f"{data_name}_rels.txt"), "r", encoding="utf-8") as fp:
                    self.rels = json.load(fp)

    def ner_tokenizer(self, text):
        # print("文本长度需要小于：{}".format(self.max_seq_len))
        text = text[:self.max_seq_len - 2]
        text = ["[CLS]"] + [i for i in text] + ["[SEP]"]
        tmp_input_ids = self.tokenizer.convert_tokens_to_ids(text)
        input_ids = tmp_input_ids + [0] * (self.max_seq_len - len(tmp_input_ids))
        attention_mask = [1] * len(tmp_input_ids) + [0] * (self.max_seq_len - len(tmp_input_ids))
        input_ids = torch.tensor(np.array([input_ids]))
        attention_mask = torch.tensor(np.array([attention_mask]))
        return input_ids, attention_mask

    def re_tokenizer(self, text, h, t):
        # print("文本长度需要小于：{}".format(self.max_seq_len))
        pre_length = 4 + len(h) + len(t)
        text = text[:self.max_seq_len - pre_length]
        text = "[CLS]" + h + "[SEP]" + t + "[SEP]" + text + "[SEP]"
        tmp_input_ids = self.tokenizer.tokenize(text)
        tmp_input_ids = self.tokenizer.convert_tokens_to_ids(tmp_input_ids)
        input_ids = tmp_input_ids + [0] * (self.max_seq_len - len(tmp_input_ids))
        attention_mask = [1] * len(tmp_input_ids) + [0] * (self.max_seq_len - len(tmp_input_ids))
        token_type_ids = [0] * self.max_seq_len
        input_ids = torch.tensor(np.array([input_ids]))
        token_type_ids = torch.tensor(np.array([token_type_ids]))
        attention_mask = torch.tensor(np.array([attention_mask]))
        return input_ids, attention_mask, token_type_ids

    def re_predict_common(self, text, hs, ts):
        res = []
        group = []
        tmp = []
        # 用于标识h和next_h之间是否有t
        flag = False
        next_h = None
        for i, h in enumerate(hs):
            if i + 1 < len(hs):
                next_h = hs[i + 1]
            for t in ts:
                h_start = h[1]
                h_end = h[2]
                t_start = t[1]
                t_end = t[2]
                # =============================================
                # 定义不同数据的后处理规则
                if self.data_name == "dgre":
                    # 该数据原因不会出现在设备前面
                    if t_end < h_start:
                        continue
                    if next_h and h_start < t_start < next_h[1]:
                        flag = True
                    # 如果两个设备之间有原因，当前原因在第二个设备之后
                    # 那么第一个设备的原因就不能是它，于是结束原因循环
                    if next_h and flag and t_start > next_h[2]:
                        flag = False
                        break
                # =============================================
                if h[0] == t[0] or (h[0], t[0]) in tmp:
                    continue
                tmp.append((h[0], t[1]))
                input_ids, attention_mask, token_type_ids = self.re_tokenizer(text, h[0], t[0])
                input_ids = input_ids.to(self.device)
                token_type_ids = token_type_ids.to(self.device)
                attention_mask = attention_mask.to(self.device)
                output = self.re_model(input_ids, token_type_ids, attention_mask)
                logits = output.logits
                logits = logits.detach().cpu().numpy()
                logits = np.argmax(logits, -1)
                rel = self.re_id2label[logits[0]]
                if rel != "没关系" and (h[0], t[0], rel) not in group:
                    group.append((h[0], t[0], rel))
                    res.append((h[0], t[0], rel, str(h_start) + "_" + str(h_end), str(t_start) + "_" + str(t_end)))

        return res

    # 关系预测函数
    def re_predict(self, text, ner_result):
        result = []
        if self.rels is not None:
            for k, v in self.rels.items():
                ent = k.split("_")
                ent1 = ent[0]
                ent2 = ent[1]
                if ent1 in ner_result and ent2 in ner_result:
                    hs = ner_result[ent1]
                    ts = ner_result[ent2]
                    res = self.re_predict_common(text, hs, ts)
                    result.extend(res)
        else:
            labels = self.handle_ner_label(ner_result)
            for label in labels:
                ent = label.split("_")
                hs = ent[0]
                ts = ent[1]
                res = self.re_predict_common(text, ner_result[hs], ner_result[ts])
                result.extend(res)
        return result
    def handle_ner_label(self, ner_result):
        labels = [k for k, v in ner_result.items()]
        print(labels)
        combinations = []
        for i in range(len(labels)):
            for j in range(i + 1, len(labels)):
                combinations.append(labels[i] + "_" + labels[j])
        return combinations
    # 实体预测函数
    def ner_predict(self, text):
        input_ids, attention_mask = self.ner_tokenizer(text)
        input_ids = input_ids.to(self.device)
        attention_mask = attention_mask.to(self.device)
        output = self.ner_model(input_ids, attention_mask)
        attention_mask = attention_mask.detach().cpu().numpy()
        length = sum(attention_mask[0])
        logits = output.logits
        logits = logits[0][1:length - 1]
        logits = [self.ner_id2label[i] for i in logits]
        entities = get_entities(logits)
        result = {}
        for ent in entities:
            ent_name = ent[0]
            ent_start = ent[1]
            ent_end = ent[2]
            if ent_name not in result:
                result[ent_name] = [("".join(text[ent_start:ent_end + 1]), ent_start, ent_end)]
            else:
                result[ent_name].append(("".join(text[ent_start:ent_end + 1]), ent_start, ent_end))
        return result

# 输出模型
class ModelOutput:
    def __init__(self, logits, labels, loss=None):
        self.logits = logits
        self.labels = labels
        self.loss = loss

# 实体bert模型
class BertNer(nn.Module):
    def __init__(self, args):
        super(BertNer, self).__init__()
        self.bert = BertModel.from_pretrained(args.bert_dir)
        self.bert_config = BertConfig.from_pretrained(args.bert_dir)
        hidden_size = self.bert_config.hidden_size
        self.lstm_hiden = 128
        self.max_seq_len = args.max_seq_len
        self.bilstm = nn.LSTM(hidden_size, self.lstm_hiden, 1, bidirectional=True, batch_first=True, dropout=0.1)
        self.linear = nn.Linear(self.lstm_hiden * 2, args.num_labels)
        self.crf = CRF(args.num_labels, batch_first=True)

    def forward(self, input_ids, attention_mask, labels=None):
        bert_output = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        seq_out = bert_output[0]  # [batchsize, max_len, 768]
        batch_size = seq_out.size(0)
        seq_out, _ = self.bilstm(seq_out)
        seq_out = seq_out.contiguous().view(-1, self.lstm_hiden * 2)
        seq_out = seq_out.contiguous().view(batch_size, self.max_seq_len, -1)
        seq_out = self.linear(seq_out)
        logits = self.crf.decode(seq_out, mask=attention_mask.bool())
        loss = None
        if labels is not None:
            loss = -self.crf(seq_out, labels, mask=attention_mask.bool(), reduction='mean')
        model_output = ModelOutput(logits, labels, loss)
        return model_output

# 关系bert模型
class BertRe(nn.Module):
    def __init__(self, args):
        super(BertRe, self).__init__()
        self.bert = BertModel.from_pretrained(args.bert_dir)
        self.bert_config = BertConfig.from_pretrained(args.bert_dir)
        self.hidden_size = self.bert_config.hidden_size
        self.linear = nn.Linear(self.hidden_size, args.num_labels)
        self.criterion = nn.CrossEntropyLoss()

    def forward(self,
                input_ids,
                attention_mask,
                token_type_ids,
                labels=None):
        bert_output = self.bert(input_ids=input_ids)
        seq_out = bert_output[1]  # [batchsize, 768]
        seq_out = self.linear(seq_out)
        loss = None

        if labels is not None:
            loss = self.criterion(seq_out, labels)
        model_output = ModelOutput(seq_out, labels, loss)
        return model_output


class NerClass:
    def __init__(self, label, start, end, property):
        self.label = label
        self.start = start
        self.end = end
        self.property = property

    def to_json(self):
        return {
            "label": self.label,
            "start": self.start,
            "end": self.end,
            "property": self.property
        }

class ReClass:
    def __init__(self, s_label, s_property, t_label, t_property, rel, position):
        self.s_label = s_label
        self.s_property = s_property
        self.t_label = t_label
        self.t_property = t_property
        self.rel = rel
        self.position = position


    def to_json(self):
        return {
            "s_label": self.s_label,
            "s_property": self.s_property,
            "t_label": self.t_label,
            "t_property": self.t_property,
            "rel": self.rel,
            "position": self.position
        }



