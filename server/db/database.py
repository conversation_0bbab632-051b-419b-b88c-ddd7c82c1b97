import pymysql
from config.config_db import load_config
from config.config_env import env_name  # 导入环境名称变量

# 从配置文件加载数据库配置
db_host, db_port, db_user, db_password, db_database = load_config(env_name)


import pymysql
from config.config_db import load_config

class Database:
    _instance = None

    def __new__(cls, host, port, user, password, database):
        if not cls._instance:
            cls._instance = super(Database, cls).__new__(cls)
        return cls._instance

    def __init__(self, host, port, user, password, database):
        """
        初始化 Database 类的实例。

        Args:
            host (str): 数据库主机名。
            port (int): 数据库端口号。
            user (str): 数据库用户名。
            password (str): 数据库密码。
            database (str): 数据库名称。
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.connect()

    def connect(self):
        self.connection = pymysql.connect(
            host=self.host,
            port=self.port,
            user=self.user,
            password=self.password,
            database=self.database
            
        )
        self.cursor = self.connection.cursor()

    def disconnect(self):
        """
        断开数据库连接。
        """
        self.cursor.close()
        self.connection.close()

    def execute_query(self, query):
        """
        执行查询并返回结果集。

        Args:
            query (str): 要执行的查询语句。

        Returns:
            tuple: 查询结果集。
        """
        self.cursor.execute(query)
        result = self.cursor.fetchall()
        return result

    def execute_insert(self, table, columns, values):
        """
        执行插入操作。

        Args:
            table (str): 要插入数据的表名。
            columns (list): 列名列表。
            values (list): 插入的值列表。

        Returns:
            int: 新插入行的ID。
        """
        query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(['%s'] * len(values))})"
        self.cursor.execute(query, values)
        self.connection.commit()
        return self.cursor.lastrowid

    def execute_update(self, table, set_values, condition):
        """
        执行更新操作。

        Args:
            table (str): 要更新的表名。
            set_values (dict): 要设置的值。
            condition (str): 更新条件。

        Returns:
            None
        """
        set_clause = ', '.join([f"{column} = %s" for column in set_values.keys()])
        query = f"UPDATE {table} SET {set_clause} WHERE {condition}"
        self.cursor.execute(query, list(set_values.values()))
        self.connection.commit()

    def execute_delete(self, table, condition):
        """
        执行删除操作。

        Args:
            table (str): 要删除数据的表名。
            condition (str): 删除条件。

        Returns:
            None
        """
        query = f"DELETE FROM {table} WHERE {condition}"
        self.cursor.execute(query)
        self.connection.commit()

# 创建 Database 类的单例实例
db = Database(db_host, db_port, db_user, db_password, db_database)