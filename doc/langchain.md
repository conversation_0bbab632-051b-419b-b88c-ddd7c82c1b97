# Lang<PERSON><PERSON><PERSON> 的作者是 Harrison Chase，最初是于 2022 年 10 月开源的一个项目，在 GitHub 上获得大量关注之后迅速转变为一家初创公司。2017 年 Harrison Chase 还在哈佛上大学，如今已是硅谷的一家热门初创公司的 CEO，这对他来说是一次重大而迅速的跃迁。Insider 独家报道，人工智能初创公司 LangChain 在种子轮一周后，再次获得红杉领投的 2000 万至 2500 万美元融资，估值达到 2 亿美元。

LangChain 目前是有两个语言版本（python 和 nodejs）,从下图可以看出来，短短半年的时间该项目的 python 版本已经获得了 54k+的 star。nodejs 版本也在短短 4 个月收货了 7k+的 star，这无疑利好前端同学，不需要会 python 也能快速上手 LLM 应用开发。

LangChain 包含六部分组成，分别为：Models、Prompts、Indexes、Memory、Chains、Agents。

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/ROwYklr4kIg8NjGE/b0e02b678da44c97b858d214bc79108a1072.png)

# 1.Models（模型）

下面我们以具体示例分别阐述下 Chat Modals, Embeddings, LLMs。

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/ROwYklr4kIg8NjGE/4551d2d69700454886d586c0c0f842be1072.png)

聊天模型：

LangChain 为使用聊天模型提供了一个标准接口。聊天模型是语言模型的一种变体。虽然聊天模型在内部使用语言模型，但它们所提供的接口略有不同。它们不是暴露一个 "输入文本，输出文本" 的 API，而是提供了一个以 "聊天消息" 作为输入和输出的接口。

聊天模型的接口是基于消息而不是原始文本。LangChain 目前支持的消息类型有 AIMessage、HumanMessage、SystemMessage 和 ChatMessage，其中 ChatMessage 接受一个任意的角色参数。大多数情况下，您只需要处理 HumanMessage、AIMessage 和 SystemMessage。

embedding：

这个更多的是用于文档、文本或者大量数据的总结、问答场景，一般是和向量库一起使用，实现向量匹配。其实就是把文本等内容转成多维数组，可以后续进行相似性的计算和检索。他相比 fine-tuning 最大的优势就是，不用进行训练，并且可以实时添加新的内容，而不用加一次新的内容就训练一次，并且各方面成本要比 fine-tuning 低很多。

LLMS：

LLMS 是 LangChain 的核心，从官网可以看到 LangChain 继承了非常多的大语言模型。

# 2. Indexes（索引）

索引是指对文档进行结构化的方法，以便 LLM 能够更好的与之交互。该组件主要包括：Document Loaders（文档加载器）、Text Splitters（文本拆分器）、VectorStores（向量存储器）以及 Retrievers（检索器）。

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/ROwYklr4kIg8NjGE/b896287f5921495abdc5411f693c27c31072.png)

# 3. Chains（链）

链允许我们将多个组件组合在一起以创建一个单一的、连贯的任务。例如，我们可以创建一个链，它接受用户输入，使用 PromptTemplate 对其进行格式化，然后将格式化的响应传递给 LLM。另外我们也可以通过将多个链组合在一起，或者将链与其他组件组合来构建更复杂的链。

LLMChain：

LLMChain 是一个简单的链，它围绕语言模型添加了一些功能。它在整个 LangChain 中广泛使用，包括在其他链和代理中。它接受一个提示模板，将其与用户输入进行格式化，并返回 LLM 的响应。

●**call**方法返回输入和输出键值。

另外可以通过将 return\_only\_outputs 设置为 True，可以将其配置为只返回输出键值。

llm\_chain("corny", return\_only\_outputs=True){'text': 'Why did the tomato turn red? Because it saw the salad dressing!'}

● run 方法返回的是字符串而不是字典。

llm\_chain.run({"adjective": "corny"})'Why did the tomato turn red? Because it saw the salad dressing!'

● apply 方法允许你对一个输入列表进行调用

input\_list = \[{"product": "socks"},{"product": "computer"},{"product": "shoes"}

llm\_chain.apply(input\_list)\[{'text': '\n\nSocktastic!'},{'text': '\n\nTechCore Solutions.'},{'text': '\n\nFootwear Factory.'}\]

● generate 方法类似于 apply 方法，但它返回的是 LLMResult 而不是字符串。LLMResult 通常包含有用的生成信息，例如令牌使用情况和完成原因。

llm\_chain.generate(input\_list)LLMResult(generations=\[\[Generation(text='\n\nSocktastic!', generation\_info={'finish\_reason': 'stop', 'logprobs': None})\], \[Generation(text='\n\nTechCore Solutions.', generation\_info={'finish\_reason': 'stop', 'logprobs': None})\], \[Generation(text='\n\nFootwear Factory.', generation\_info={'finish\_reason': 'stop', 'logprobs': None})\]\], llm\_output={'token\_usage': {'prompt\_tokens': 36, 'total\_tokens': 55, 'completion\_tokens': 19}, 'model\_name': 'text-davinci-003'})

● predict 方法类似于 run 方法，不同之处在于输入键被指定为关键字参数，而不是一个 Python 字典。

# Single input examplellm\_chain.predict(product="colorful socks")

SimpleSequentialChain：

顺序链的最简单形式，其中每个步骤都有一个单一的输入/输出，并且一个步骤的输出是下一步的输入。

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/collab/AJdl64YMaaJmqke1/b819eea1-17e7-49d3-8b84-a958ed08c4e0.jpg)

     TransformChain：

转换链允许我们创建一个自定义的转换函数来处理输入，将处理后的结果用作下一个链的输入。如下示例我们将创建一个转换函数，它接受超长文本，将文本过滤为仅前 3 段，然后将其传递到 LLMChain 中以总结这些内容。

# 4. Memory（记忆）

熟悉 openai 的都知道,openai 提供的聊天接口 api，本身是不具备“记忆的”能力。如果想要使聊天具有记忆功能，则需要我们自行维护聊天记录，即每次把聊天记录发给 gpt。

langchain 提供了不同的 Memory 组件完成内容记忆，如下是目前提供的组件。

ConversationBufferMemory

该组件类似我们上面的描述，只不过它会将聊天内容记录在内存中，而不需要每次再手动拼接聊天记录。

ConversationBufferWindowMemory

相比较第一个记忆组件，该组件增加了一个窗口参数，会保存最近看 k 论的聊天内容。

ConversationTokenBufferMemory

在内存中保留最近交互的缓冲区，并使用 token 长度而不是交互次数来确定何时刷新交互。

ConversationSummaryMemory

相比第一个记忆组件，该组件只会存储一个用户和机器人之间的聊天内容的摘要。

ConversationSummaryBufferMemory

结合了上面两个思路，存储一个用户和机器人之间的聊天内容的摘要并使用 token 长度来确定何时刷新交互。

VectorStoreRetrieverMemory

它是将所有之前的对话通过向量的方式存储到 VectorDB（向量数据库）中，在每一轮新的对话中，会根据用户的输入信息，匹配向量数据库中最相似的 K 组对话。

# 5. Agents（代理）

一些应用程序需要根据用户输入灵活地调用 LLM 和其他工具的链。代理接口为这样的应用程序提供了灵活性。代理可以访问一套工具，并根据用户输入确定要使用哪些工具。我们可以简单的理解为它可以动态的帮我们选择和调用 chain 或者已有的工具。代理主要有两种类型 Action agents 和 Plan-and-execute agents。

参考资料：

一文入门最热的LLM应用开发框架LangChain：[https://www.163.com/dy/article/ICC5CD6N0518R7MO.html](https://www.163.com/dy/article/ICC5CD6N0518R7MO.html)