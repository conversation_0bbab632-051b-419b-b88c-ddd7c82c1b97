# -*- coding：utf-8 -*-
from imp import reload

from langchain_core.output_parsers.json import parse_json_markdown
from server.prompt.prompt_dynamic import redis_prompt_and_format, prompt_and_format
from server.prompt.prompt_extract import extract_prompt_and_format
from utils.utils import ArgsObject
from server import models
import utils.document_loader as document
import requests
import cv2
import numpy as np
from paddleocr import PPStructure,save_structure_res

from docx import Document
import os
from docx.oxml.ns import qn
from io import BytesIO
import pandas as pd
from openpyxl import load_workbook
from bs4 import BeautifulSoup



args = ArgsObject()
table_engine = PPStructure(layout=False, show_log=True)
# 生成待办
# def llm_label(msg):
#
#     args.llm_name = "QIANFAN"
#     # 模型初始化
#     llm = getattr(models, args.llm_name)(args).llm
#
#     # 研发二部负责明天开展重点工作产品开发，完成产品发版上线
#     final_prompt = MEETING_PROMPT_FORMAT.format(context=msg.text, current_time=datetime.now().strftime("%Y%m%d%H%M%S"))
#
#     return output_parsers.parse(llm(final_prompt))

# 短文本
def llm_label(msg):
    args.llm_name = "QIANFAN"
    # 模型初始化
    llm = getattr(models, args.llm_name)(args).llm
    result = []
    for text in msg.texts:
        model = {}
        final_prompt = redis_prompt_and_format(msg.prompt_key, text)
        llm = llm(final_prompt)
        model.setdefault("content", text)
        model.setdefault("output", parse_json_markdown(llm))
        result.append(model)

    print(result)
    return result

# 读取文档
def llm_document(msg):
    args.llm_name = "QIANFAN"
    args.model = msg.model
    # 模型初始化
    llm = getattr(models, args.llm_name)(args).llm
    file = document.load(msg.fileUrl)
    print(file)
    final_prompt = prompt_and_format(msg.prompt, file)
    result = llm(final_prompt)
    print(result)
    return parse_json_markdown(result)

# 文档抽取
def llm_extract_txt(msg):
    args.llm_name = "QIANFAN"
    args.model = msg.model
    # 模型初始化
    llm = getattr(models, args.llm_name)(args).llm
    file = document.load(msg.filePath)
    print(file)
    final_prompt = extract_prompt_and_format(msg.prompt, file, msg.item)
    result = llm(final_prompt)
    print(result)
    return parse_json_markdown(result)

# 表格抽取
def llm_extract_table(msg):
    args.llm_name = "QIANFAN"
    args.model = msg.model
    # 模型初始化
    llm = getattr(models, args.llm_name)(args).llm

    result = handle_file_by_type(msg.filePath)
    print(result)

    final_prompt = extract_prompt_and_format(msg.prompt, result, msg.item)
    result = llm(final_prompt)
    print(result)
    return parse_json_markdown(result)


def handle_file_by_type(filePath):
    # 获取文件后缀
    file_extension = filePath.split('.')[-1].lower()

    # 判断文件类型并执行不同逻辑
    if file_extension in ['jpg', 'jpeg', 'png', 'gif']:
        return handle_image(filePath)
    elif file_extension == 'pdf':
        return handle_pdf(filePath)
    elif file_extension in ['doc', 'docx']:
        return handle_word(filePath)
    elif file_extension in ['xls', 'xlsx']:
        return handle_excel(filePath)
    else:
        return "Unsupported file format"
    

# 处理图片文件的逻辑
def handle_image(filePath):
    # 在这里添加图片处理逻辑，比如下载、查看或分析图片
    response = requests.get(filePath)
    img_array = np.frombuffer(response.content, np.uint8)
    img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)

    result = table_engine(img)
    return result[0]['res']['html']

# 处理PDF文件的逻辑
def handle_pdf(filePath):
    # 在这里添加PDF处理逻辑，比如解析、提取文本等
    file = document.load(filePath)
    return file

# 处理Word文件的逻辑
def handle_word(filePath):
    # 在这里添加Word处理逻辑，比如读取文档内容等
    response = requests.get(filePath)
    # 检查请求是否成功
    if response.status_code == 200:
        # 将下载的内容保存到本地临时文件
        with open("temp_document.docx", "wb") as f:
            f.write(response.content)
        document = Document("temp_document.docx")
        # 提取表格信息
        tables = document.tables
        # 将表格信息转换为HTML
        html_content = "<html><head><title>Table from Word</title></head><body>"
        for table_idx, table in enumerate(tables):
            html_content += f"<h2>Table {table_idx + 1}</h2>"
            html_content += "<table border='1'>"
            for row in table.rows:
                html_content += "<tr>"
                for cell in row.cells:
                    if is_merged(cell):
                        vmerge, gridspan = get_cell_span(cell)
                        if gridspan:
                            colspan = int(gridspan.val)
                            html_content += f"<td colspan='{colspan}'>{cell.text}</td>"
                        else:
                            html_content += f"<td>{cell.text}</td>"
                    else:
                        html_content += f"<td>{cell.text}</td>"
                html_content += "</tr>"
            html_content += "</table><br>"

        html_content += "</body></html>"

        # 打印或保存HTML内容
        print(html_content)
        return html_content

# 处理Excel文件的逻辑
def handle_excel(filePath):
    # 在这里添加Excel处理逻辑，比如读取表格、分析数据等
    # 从URL下载Excel文件到内存
    excel_file = download_excel_from_url(filePath)
    wb = load_workbook(filename=excel_file)
    sheet = wb.active

    # 创建一个空的DataFrame
    data = pd.DataFrame(sheet.values)

    # 获取合并单元格的信息
    merged_cells = sheet.merged_cells.ranges

    # 初始化HTML表格字符串
    html = '<table border="1">'

    # 逐行处理
    for i, row in data.iterrows():
        html += '<tr>'
        for j, cell in enumerate(row):
            # 检查当前单元格是否在合并区域内
            is_merged = False
            for merged_cell in merged_cells:
                if (merged_cell.min_row - 1 <= i <= merged_cell.max_row - 1) and (merged_cell.min_col - 1 <= j <= merged_cell.max_col - 1):
                    if (merged_cell.min_row - 1 == i) and (merged_cell.min_col - 1 == j):
                        rowspan = merged_cell.max_row - merged_cell.min_row + 1
                        colspan = merged_cell.max_col - merged_cell.min_col + 1
                        html += f'<td rowspan="{rowspan}" colspan="{colspan}">{cell}</td>'
                    is_merged = True
                    break
            if not is_merged:
                html += f'<td>{cell}</td>'
        html += '</tr>'

    html += '</table>'

    # 使用BeautifulSoup美化HTML代码
    soup = BeautifulSoup(html, 'html.parser')
    pretty_html = soup.prettify()

    return pretty_html

################## word处理-辅助处理start ##################
def is_merged(cell):
    return (cell._element.get(qn('w:vMerge')) or cell._element.get(qn('w:gridSpan'))) is not None

def get_cell_span(cell):
    vmerge = cell._element.get(qn('w:vMerge'))
    gridspan = cell._element.get(qn('w:gridSpan'))
    return vmerge, gridspan
################## word处理-辅助处理end ##################

################## excel处理-辅助处理start ##################
# 下载文件并返回字节数据
def download_excel_from_url(url):
    response = requests.get(url)
    if response.status_code == 200:
        return BytesIO(response.content)  # 将内容存储在内存中
    else:
        raise Exception(f"Failed to download file: {response.status_code}")
################## excel处理-辅助处理end ##################
