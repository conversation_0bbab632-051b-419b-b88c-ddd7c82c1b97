import os
from langchain.llms import Qi<PERSON><PERSON>LLMEndpoint
from config.model_config import LLM_MODEL, TEMPERATURE

config = LLM_MODEL.get("qianfan", {}).copy()

# qianfan sdk 鉴权
os.environ["QIANFAN_AK"] = config["api_key"]
os.environ["QIANFAN_SK"] = config["secret_key"]

# 文心千帆
class QIANFAN(object):
    def __init__(self, args):
        """
        初始化MODEL类
        """
        super(QIANFAN, self).__init__()
        self.args = args
        self.llm = QianfanLLMEndpoint(endpoint=config["version"])