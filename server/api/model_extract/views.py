
from fastapi import APIRouter, Depends, Header, Request, UploadFile, HTTPException

from pydantic import BaseModel
from typing import Optional
from server.service.model_extract import model_extract


router = APIRouter()

class User(BaseModel):
    nick_name: str

class Req(BaseModel):
    data_name: str
    model_type: Optional[str] = None
    texts: list


"""
抽取模型服务
"""
@router.post("/model/extract", summary="模型抽取")
async def get_label(msg: Req):
    print(msg)
    data = model_extract(msg.texts, msg.data_name, msg.model_type)
    return data
