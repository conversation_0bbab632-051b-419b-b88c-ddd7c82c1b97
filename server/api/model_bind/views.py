
from fastapi import APIRouter, Depends, Header, Request, UploadFile, HTTPException
from starlette.responses import StreamingResponse
import json

router = APIRouter()

class User(BaseModel):
    nick_name: str

class Req(BaseModel):
    query: str
    knowledge: dict
    user: User


"""
模型绑定
"""
@router.post("/model/bind", summary="模型绑定")
async def get_label(msg: Req):
    print(msg)

    return data

