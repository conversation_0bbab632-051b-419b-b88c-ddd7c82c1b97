import os
from langchain.llms import Tongyi
from config.model_config import LLM_MODEL, TEMPERATURE

config = LLM_MODEL.get("qianfan", {}).copy()

# TONGYI sdk 鉴权
os.environ["QIANFAN_AK"] = config["api_key"]
os.environ["QIANFAN_SK"] = config["secret_key"]
# 通义千问
class TONGYI(object):
    def __init__(self, args):
        """
        初始化MODEL类
        """
        super(TONGYI, self).__init__()
        self.args = args
        self.llm = Tongyi(model=config["version"], temperature=TEMPERATURE)