import configparser


def load_config(env):
    """
    根据给定的环境加载配置。

    Args:
        env (str): 要加载的环境名称。

    Returns:
        tuple: 包含数据库配置的元组 (db_host, db_port, db_user, db_password, db_database)。
    """

    # 从配置文件加载数据库配置
    config = configparser.ConfigParser()
    config.read('config/config.ini')

    section_name = env.lower()
    if section_name not in config:
        raise ValueError(f"在配置文件中找不到环境 '{env}'。")

    section = config[section_name]
    db_host = section.get('db_host')
    db_port = section.getint('db_port')
    db_user = section.get('db_user')
    db_password = section.get('db_password')
    db_database = section.get('db_database')

    return db_host, db_port, db_user, db_password, db_database

def load_redis_config(env):
    # 从配置文件加载数据库配置
    config = configparser.ConfigParser()
    config.read('config/config.ini')

    section_name = env.lower()
    if section_name not in config:
        raise ValueError(f"在配置文件中找不到环境 '{env}'。")

    section = config[section_name]
    redis_host = section.get('redis_host')
    redis_port = section.getint('redis_port')
    redis_password = section.get('redis_password')
    redis_database = section.get('redis_database')

    return redis_host, redis_port, redis_password, redis_database

def load_neo4j_config(env):
    # 从配置文件加载数据库配置
    config = configparser.ConfigParser()
    config.read('config/config.ini')

    section_name = env.lower()
    if section_name not in config:
        raise ValueError(f"在配置文件中找不到环境 '{env}'。")

    section = config[section_name]
    neo4j_url = section.get('neo4j_url')
    neo4j_username = section.get('neo4j_username')
    neo4j_password = section.get('neo4j_password')
    return neo4j_url, neo4j_username, neo4j_password
