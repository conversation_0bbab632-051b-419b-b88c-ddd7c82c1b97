
from fastapi import APIRouter, Depends, Header, Request, UploadFile, HTTPException
from starlette.responses import StreamingResponse
import json

from pydantic import BaseModel

router = APIRouter()
class Qtest(BaseModel):
    Text: str
    User: str

@router.get("/info", summary="获取用户自己的资料")
async def getUserInfo(item: Qtest):
    print(item)
    return "验证成功"
# 模拟生成8192个时域数据
def generate_data():
    for i in range(8192):
        # 在这里生成你的时域数据
        yield str(i)

# 读取 JSON 文件的函数
def read_json_file(file_path):
    with open(file_path, 'r') as file:
        data = json.load(file)
    return data


@router.get("/data", summary="获取用户自己的资料")
async def getUserInfo():
    # 使用 StreamingResponse 发送数据流
    # return StreamingResponse(generate_data(), media_type="text/plain")
    # 指定你的 JSON 文件路径
    json_file_path = "server/api/user/data.json"
    
    # 调用读取 JSON 文件的函数
    json_data = read_json_file(json_file_path)
    
    # 返回 JSON 格式的响应
    return json_data