# -*- coding：utf-8 -*-
from imp import reload
import requests


from langchain.document_loaders import UnstructuredWordDocumentLoader, PyPDFium2Loader, TextLoader


def load(file_path):
    print("文件路径" + file_path)
    if file_path.endswith('.pdf'):
        loader = PyPDFium2Loader(file_path)
    elif file_path.endswith('.doc') or file_path.endswith('.docx'):
        response = requests.get(file_path)

        # 检查请求是否成功
        if response.status_code == 200:
            # 将下载的内容保存到本地临时文件
            with open("temp_document.docx", "wb") as f:
                f.write(response.content)
            loader = UnstructuredWordDocumentLoader("temp_document.docx")
        
    elif file_path.endswith('.txt'):
        loader = TextLoader(file_path)
    else:
        raise ValueError('Unsupported file type')
    return loader.load()

