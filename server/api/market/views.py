
from fastapi import APIRouter, Depends, Header, Request, UploadFile, HTTPException
from starlette.responses import StreamingResponse
import json

from pydantic import BaseModel
from server.service.gbi_market import market


router = APIRouter()

class User(BaseModel):
    nick_name: str

class Req(BaseModel):
    query: str
    knowledge: dict
    user: User


"""
1、统计订单日期为2020-11-30日的销售额
2、销售额排名前5的省份是哪些？
3、超市昨天商品摇椅是否有销售额
    SELECT SUM(sales) as total_sales
    FROM supper_market_info
    WHERE trade_name LIKE '%摇椅%' AND DATE(order_date) = CURDATE() - INTERVAL 1 DAY
4、"query": "统计办公用品的利润率",
  "knowledge": {"利润率": "计算方式: 利润/销售额"},

"""
@router.post("/qa", summary="问数")
async def get_data(msg: Req):
    print(msg)
    data = market(msg)

    return data

