from fastapi import APIRouter
from pydantic import BaseModel
from server.service.llm_qa import llm_qa

router = APIRouter()

class User(BaseModel):
    nick_name: str

class Req(BaseModel):
    query: str
    graphTypes: list
    graphId: int


"""
智能问答
"""
@router.post("/llm/qa", summary="智能问答")
async def get_qa(msg: Req):
    print(msg)
    data = llm_qa(msg.query, msg.graphTypes, msg.graphId)
    return data



##你是一个实体关系五元组抽取模型，请按照如下步骤执行。

##第一步：根据以下文档，抽取出项目、项目概述、人员配置、客户名称、客户地址、客户方项目经理、项目交付目标，抽取的实体必须完全匹配，严禁随意发挥。

##【文档】：{file}

##第二步：根据第一步的结果，按照本体设计组合成五元组，输出格式 (Output Format): {format_instructions}，如果识别多条用[]表示，不要随意发挥；

##本体设计： (项目)-[项目概述]-&gt;(项目概述) 、(项目)-[团队成员]-&gt;(人员配置)、(项目)-[客户名称]-&gt;(客户名称)、(项目)-[客户地址]-&gt;(客户地址)、(项目)-[客户方项目经理]-&gt;(客户方项目经理)、(项目)-[项目交付目标]-&gt;(项目交付目标)
