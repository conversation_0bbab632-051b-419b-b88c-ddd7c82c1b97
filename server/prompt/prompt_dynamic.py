from server.parser.parser_common import FORMAT_COMMON_INSTRUCTIONS
from server.parser.parser_dynamic import format_dynamic_instructions
from langchain.prompts import PromptTemplate
from server.db.redis import r
import json


def redis_prompt(promote_key, text):
    if r.exists(promote_key) == 0:
        print(f" {promote_key} prompt template does not exist.")
    input_prompt = PromptTemplate(
        input_variables=["context"],
        partial_variables={"format_instructions": FORMAT_COMMON_INSTRUCTIONS},
        template=json.loads(r.get(promote_key)),
    )
    return input_prompt.format(context=text)

DEMO_PROMPT_PROJECT_ST = """
##你是一名项目章程图谱构建专家，请按照如下步骤执行。

##根据以下文档，抽取出项目、项目概述、人员配置、客户名称、客户地址、客户方项目经理、项目交付目标，涉及到人员要明确姓名，抽取的实体必须完全匹配，严禁随意发挥，结果以json格式输出。

##【文档】：{file}
"""

DEMO_PROMPT_PROJECT = """
##你是一名项目章程图谱构建专家，请按照如下步骤执行。

##第一步：根据以下文档，抽取出项目、项目概述、人员配置、客户名称、客户地址、客户方项目经理、项目交付目标，抽取的实体必须完全匹配，严禁随意发挥。

##【文档】：{file}

##第二步：根据第一步的结果，按照本体设计组合成五元组，输出格式 (Output Format): {format_instructions}，如果识别多条用[]表示，输出格式属性不允许重复输出，不要随意发挥；

##本体设计： (项目)-[项目概述]-&gt;(项目概述) 、(项目)-[团队成员]-&gt;(人员配置)、(项目)-[客户名称]-&gt;(客户名称)、(项目)-[客户地址]-&gt;(客户地址)、(项目)-[客户方项目经理]-&gt;(客户方项目经理)、(项目)-[项目交付目标]-&gt;(项目交付目标)
"""

DEMO_PROMPT = """
##你是一个实体关系五元组抽取模型，请按照如下步骤执行。

##第一步：根据以下文档，抽取出合同名称、项目名称、合同金额、付款方式。不要随意发挥。

##【文档】：{file}

##第二步：根据第一步的结果，严格参考本体设计确定头实体、尾实体和关系，输出五元组（头实体,头实体类型,关系,尾实体,尾实体类型），识别结果外层用[]表示，不要随意发挥。
##【本体设计】
##（项目,项目实施,合同），（合同 ,合同约定金额,合同金额），（合同,合同约定付款方式,付款方式）
"""
# ```json
# [
#     {
#         "s_label": "智慧港航物流系统技术委托开发合同",
#         "s_property": "项目",
#         "t_label": "智慧港航物流系统",
#         "t_property": "项目实施",
#         "rel": "合同"
#     },
#     {
#         "s_label": "智慧港航物流系统技术委托开发合同",
#         "s_property": "合同",
#         "t_label": "600000元",
#         "t_property": "合同约定金额",
#         "rel": "合同金额"
#     },
#     {
#         "s_label": "智慧港航物流系统技术委托开发合同",
#         "s_property": "合同",
#         "t_label": "电汇、承兑及其他",
#         "t_property": "合同约定付款方式",
#         "rel": "付款方式"
#     }
# ]
# ```

DEMO_PROMPT2 = """
##你是一个实体关系五元组抽取模型，请按照如下步骤执行。

##第一步：根据以下文档，抽取出合同、项目、合同最终结算价格、付款方式。请从下面的文档中抽取object和subject，不要随意发挥。

##【文档】：{file}

##第二步：根据第一步的结果，按照本体设计组合成五元组，输出格式 (Output Format): {format_instructions}，如果识别多条用[]表示，不要随意发挥；

##本体设计：项目 ----&gt;合同（关系为：项目实施）、合同 ----&gt;合同最终结算价格（关系为：合同约定金额）、合同 ----&gt;付款方式（关系为：合同约定付款方式）
"""

DEMO_PROMPT3 = """
##你是一个实体关系五元组抽取模型，请按照如下步骤执行。

##第一步：根据以下文档，抽取出合同、项目、合同最终结算价格、付款方式。不要随意发挥。

##【文档】：{file}

##第二步：根据第一步的结果，按照本体设计组合成五元组，输出格式 (Output Format): {format_instructions}，如果识别多条用[]表示，不要随意发挥；

##本体设计：项目-[项目实施]->合同 、合同-[合同约定合同价格]->合同最终结算价格 、合同-[合同约定付款方式]->付款方式

"""

def demo(file):
    input_prompt = PromptTemplate(
        input_variables=["file"],
        partial_variables={"format_instructions": format_dynamic_instructions("subject", "subject_type", "relation", "object", "object_type")},
        template=DEMO_PROMPT3
    )

    print(input_prompt)
    return input_prompt.format(file=file)

def redis_prompt_and_format(prompt_key, file):
    if r.exists(prompt_key) == 0:
        print(f" {prompt_key} prompt template does not exist.")
    # v = json.loads(r.get(format_key))

    input_prompt = PromptTemplate(
        input_variables=["file"],
        partial_variables={"format_instructions": format_dynamic_instructions("subject", "subject_type", "relation", "object", "object_type")},
        template=json.loads(r.get(prompt_key))
    )
    print(input_prompt)
    return input_prompt.format(file=file)


def prompt_and_format(prompt, file):
    input_prompt = PromptTemplate(
        input_variables=["file"],
        template=prompt
    )
    print(input_prompt)
    return input_prompt.format(file=file)
