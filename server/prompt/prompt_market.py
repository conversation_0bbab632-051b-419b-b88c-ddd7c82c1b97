from langchain.prompts import PromptTemplate

template_market = """
角色 (Role): 
你是一名数据分析师
任务 (Task): 
根据用户问题和数据库的查询结果给出答案。
要求 (Requirement): 
根据结果灵活返回数据结果，可以采取表格等格式，给出总结性的描述

【用户问题】：{query}
【数据库查询结果】：{result}
【回答】：
"""

MARKET_PROMPT = PromptTemplate(
    input_variables=["context"],
    template=template_market,
)


template_prompt_file = """
角色 (Role): 
你是一名知识抽取专家
任务 (Task): 
根据以下文档，抽取出合同名称、项目名称、合同金额、付款方式。
要求 (Requirement): 
请从下面的文档中抽取，不要随意发挥，除此外需要按照如下格式。

【文档】：{file}
"""

PROMPT_FILE = PromptTemplate(
    input_variables=["file"],
    template=template_prompt_file,
)

template_five_group = """
##你是一个实体关系五元组抽取模型。我会给你头实体类型列表subject_types，尾实体类型列表object_types，关系列表relations，从文档中抽取出符合以上类型的五元组。

##请你根据这三个列表抽出文档中的subject和object，并组成五元组，且形式为(subject, subject_type, relation, object, object_type)。 

##头实体类型包括：subject_types：['合同名称', '项目名称'] 

##关系类型包括：relations：['合同约定的价格', '合同约定的付款方式', '项目实施'] 

##尾实体类型包括：object_types：[ '合同名称', '付款方式', '合同金额']

##除了这个列表以外请不要输出别的多余的话。

##【文档】：{file}
"""

PROMPT_FIVE_GROUP = PromptTemplate(
    input_variables=["file"],
    template=template_five_group,
)


template_five_group_chain = """
##你是一个实体关系五元组抽取模型，请按照如下步骤执行。

##第一步：根据以下文档，抽取出合同名称、项目名称、合同金额、付款方式。请从下面的文档中抽取object和subject，不要随意发挥。

##【文档】：{file}

##第二步：根据第一步的结果，按照本体设计组合成五元组，输出格式 (Output Format): {format_instructions}，如果识别多条用[]表示，不要随意发挥；

##本体设计：项目名称 ---->合同名称（关系为：项目实施）、合同名称 ---->合同金额（关系为：合同约定金额）、合同名称 ---->付款方式（关系为：合同约定付款方式）
"""

MARKET_PROMPT_FIVE_GROUP_CHAIN = PromptTemplate(
    input_variables=["file"],
    template=template_five_group_chain,
)
