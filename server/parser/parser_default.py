from langchain.output_parsers import StructuredOutputParser, ResponseSchema

# 大模型回复结构
response_schema = [
    ResponseSchema(name="project", description="代办事项"),
    ResponseSchema(name="target", description="任务目标"),
    ResponseSchema(name="unit", description="责任单位"),
    ResponseSchema(name="completDate", description="完成期限")
]

output_parsers = StructuredOutputParser.from_response_schemas(response_schema)

MEETING_FORMAT_INSTRUCTIONS = output_parsers.get_format_instructions(only_json=True)