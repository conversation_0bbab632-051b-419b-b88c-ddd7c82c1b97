from langchain.output_parsers import StructuredOutputParser, ResponseSchema


def format_dynamic_instructions(s_label, s_property, rel, t_label, t_property):
    # 大模型回复结构
    response_schema = [
        ResponseSchema(name="s_label", description=s_label),
        ResponseSchema(name="s_property", description=s_property),
        ResponseSchema(name="t_label", description=t_label),
        ResponseSchema(name="t_property", description=t_property),
        ResponseSchema(name="rel", description=rel)
    ]
    output_parsers = StructuredOutputParser.from_response_schemas(response_schema)

    return output_parsers.get_format_instructions(only_json=True)


