import logging
import os
import time
from logging.handlers import RotatingFileHandler

script_dir = os.path.dirname(os.path.abspath(__file__))  # 获取脚本所在的目录

log_dir = os.path.join(script_dir, 'logs')  # 在脚本目录中创建一个名为logs的目录

if not os.path.exists(log_dir):
    os.mkdir(log_dir)

path = log_dir + '/%s' % time.strftime('%Y-%m-%d', time.localtime())      # 日志文件名

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 最大文件100M，保留20个
file_handler = RotatingFileHandler(path, maxBytes=1024*1024*100, backupCount=20)
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(formatter)

logger.addHandler(file_handler)

logger.info('================= 大模型agent应用平台系统日志 =================')
