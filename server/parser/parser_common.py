from langchain.output_parsers import StructuredOutputParser, ResponseSchema

# 大模型回复结构
response_schema = [
    ResponseSchema(name="s_label", description="头实体"),
    ResponseSchema(name="s_property", description="头实体类型"),
    ResponseSchema(name="t_label", description="尾实体"),
    ResponseSchema(name="t_property", description="尾实体类型"),
    ResponseSchema(name="rel", description="关系")
]

output_parsers = StructuredOutputParser.from_response_schemas(response_schema)

FORMAT_COMMON_INSTRUCTIONS = output_parsers.get_format_instructions(only_json=True)
