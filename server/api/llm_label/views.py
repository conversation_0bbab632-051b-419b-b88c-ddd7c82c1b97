
from fastapi import APIRouter

from pydantic import BaseModel
from server.service.llm_label import llm_label, llm_document, llm_extract_txt, llm_extract_table
import time


router = APIRouter()

class User(BaseModel):
    nick_name: str

class Req(BaseModel):
    texts: list
    prompt_key: str

class DocumentReq(BaseModel):
    model: str
    prompt: str
    filePath: str

class ExtractReq(BaseModel):
    model: str
    item: list
    prompt: str
    filePath: str


"""
大模型标注
"""
@router.post("/llm/label", summary="大模型标注")
async def get_label(msg: Req):
    print(msg)
    data = llm_label(msg)
    return data

@router.post("/llm/document", summary="大模型长文本抽取")
async def get_label(msg: DocumentReq):
    print("llm/document")
    data = llm_document(msg)
    return data

@router.post("/llm/extract/txt", summary="抽取任务-文本")
async def get_extract_txt(msg: ExtractReq):
    print("llm/extract/txt")
    data = llm_extract_txt(msg)
    return data

@router.post("/llm/extract/table", summary="抽取任务-表格")
async def get_extract_table(msg: ExtractReq):
    print("llm/extract/table")
    data = llm_extract_table(msg)
    return data

