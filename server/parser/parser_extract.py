from langchain.output_parsers import StructuredOutputParser, ResponseSchema

def extract_format_dynamic_instructions(extracts):
    response_schemas = [];
    for s in extracts:
      response_schemas.append(ResponseSchema(type="string", name=s, description=s))
    output_parsers = StructuredOutputParser.from_response_schemas(response_schemas)
    return output_parsers.get_format_instructions(only_json=True)